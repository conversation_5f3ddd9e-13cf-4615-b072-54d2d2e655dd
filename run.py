#!/usr/bin/env python3
"""
快速启动脚本
提供简单的命令行界面来运行抢票软件
"""

import sys
import os
import json
from ticket_bot import TicketBot


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎫 自动化抢票软件 v1.0                      ║
    ║                                                              ║
    ║  功能特性:                                                    ║
    ║  • 智能登录等待 • 移动端模拟 • 精准选择                        ║
    ║  • 智能重试机制 • 自动截图 • 详细日志                          ║
    ║                                                              ║
    ║  注意: 请确保已安装Chrome浏览器并配置好config.json              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_config():
    """检查配置文件"""
    config_file = "config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在！")
        print("请确保配置文件存在并正确配置。")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_keys = [
            'urls.concert_detail_url',
            'target_selection.session_text',
            'target_selection.ticket_tier_text',
            'target_selection.quantity'
        ]
        
        for key in required_keys:
            keys = key.split('.')
            value = config
            for k in keys:
                if k not in value:
                    print(f"❌ 配置项 {key} 缺失！")
                    return False
                value = value[k]
        
        print("✅ 配置文件检查通过")
        return True
        
    except json.JSONDecodeError:
        print("❌ 配置文件格式错误！")
        return False
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False


def show_config_summary():
    """显示配置摘要"""
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("\n📋 当前配置摘要:")
        print("-" * 50)
        print(f"演唱会详情页: {config['urls']['concert_detail_url']}")
        print(f"目标场次: {config['target_selection']['session_text']}")
        print(f"目标票档: {config['target_selection']['ticket_tier_text']}")
        print(f"目标数量: {config['target_selection']['quantity']}")
        print(f"最大重试次数: {config['retry_settings']['max_retries']}")
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ 无法显示配置摘要: {e}")


def confirm_start():
    """确认开始抢票"""
    print("\n⚠️  重要提醒:")
    print("1. 请确保网络连接稳定")
    print("2. 程序启动后会自动打开浏览器")
    print("3. 需要手动完成登录步骤")
    print("4. 登录完成后程序会自动执行抢票")
    
    while True:
        choice = input("\n是否开始抢票? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            return True
        elif choice in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y 或 n")


def main():
    """主函数"""
    print_banner()
    
    # 检查配置文件
    if not check_config():
        input("\n按回车键退出...")
        sys.exit(1)
    
    # 显示配置摘要
    show_config_summary()
    
    # 确认开始
    if not confirm_start():
        print("已取消抢票")
        input("\n按回车键退出...")
        sys.exit(0)
    
    print("\n🚀 正在启动抢票程序...")
    print("=" * 60)
    
    try:
        # 创建并启动抢票机器人
        bot = TicketBot()
        success = bot.start_ticket_purchase()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 抢票成功！")
            print("请查看浏览器确认订单状态")
            print("成功截图已保存到当前目录")
        else:
            print("❌ 抢票失败")
            print("请查看日志文件 ticket_bot.log 了解详细信息")
            print("失败截图已保存到当前目录")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断了程序")
    except Exception as e:
        print(f"\n\n❌ 程序运行出错: {e}")
        print("请查看日志文件了解详细信息")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
