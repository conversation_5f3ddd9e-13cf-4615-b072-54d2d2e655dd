"""
重试机制管理器模块
实现智能重试逻辑和错误处理
"""

import logging
import time
import functools
from typing import Callable, Any, Optional
from selenium.common.exceptions import (
    TimeoutException, 
    NoSuchElementException, 
    ElementClickInterceptedException,
    StaleElementReferenceException,
    WebDriverException
)


class RetryManager:
    """重试机制管理器类"""
    
    def __init__(self, config_manager):
        """
        初始化重试管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.logger = logging.getLogger(__name__)
        self.retry_settings = config_manager.get_retry_settings()
    
    def retry_on_failure(self, 
                        max_retries: Optional[int] = None,
                        delay: Optional[float] = None,
                        exceptions: tuple = None):
        """
        重试装饰器
        
        Args:
            max_retries: 最大重试次数
            delay: 重试间隔（秒）
            exceptions: 需要重试的异常类型
            
        Returns:
            装饰器函数
        """
        if max_retries is None:
            max_retries = self.retry_settings.get('max_retries', 3)
        if delay is None:
            delay = self.retry_settings.get('retry_delay', 2)
        if exceptions is None:
            exceptions = (
                TimeoutException,
                NoSuchElementException,
                ElementClickInterceptedException,
                StaleElementReferenceException,
                WebDriverException
            )
        
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                last_exception = None
                
                for attempt in range(max_retries + 1):
                    try:
                        result = func(*args, **kwargs)
                        if attempt > 0:
                            self.logger.info(f"{func.__name__} 在第 {attempt + 1} 次尝试后成功")
                        return result
                        
                    except exceptions as e:
                        last_exception = e
                        if attempt < max_retries:
                            self.logger.warning(
                                f"{func.__name__} 第 {attempt + 1} 次尝试失败: {e}, "
                                f"{delay} 秒后重试..."
                            )
                            time.sleep(delay)
                        else:
                            self.logger.error(
                                f"{func.__name__} 在 {max_retries + 1} 次尝试后仍然失败"
                            )
                    except Exception as e:
                        # 对于不在重试列表中的异常，直接抛出
                        self.logger.error(f"{func.__name__} 遇到不可重试的异常: {e}")
                        raise
                
                # 如果所有重试都失败，抛出最后一个异常
                raise last_exception
            
            return wrapper
        return decorator
    
    def execute_with_retry(self, 
                          func: Callable, 
                          *args, 
                          max_retries: Optional[int] = None,
                          delay: Optional[float] = None,
                          **kwargs) -> Any:
        """
        执行函数并在失败时重试
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            max_retries: 最大重试次数
            delay: 重试间隔
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        if max_retries is None:
            max_retries = self.retry_settings.get('max_retries', 3)
        if delay is None:
            delay = self.retry_settings.get('retry_delay', 2)
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                result = func(*args, **kwargs)
                if attempt > 0:
                    self.logger.info(f"{func.__name__} 在第 {attempt + 1} 次尝试后成功")
                return result
                
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    self.logger.warning(
                        f"{func.__name__} 第 {attempt + 1} 次尝试失败: {e}, "
                        f"{delay} 秒后重试..."
                    )
                    time.sleep(delay)
                else:
                    self.logger.error(
                        f"{func.__name__} 在 {max_retries + 1} 次尝试后仍然失败: {e}"
                    )
        
        raise last_exception
    
    def smart_retry_purchase(self, purchase_func: Callable, *args, **kwargs) -> bool:
        """
        智能购票重试机制
        针对购票流程的特殊重试逻辑
        
        Args:
            purchase_func: 购票函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            是否购票成功
        """
        max_retries = self.retry_settings.get('max_retries', 10)
        base_delay = self.retry_settings.get('retry_delay', 2)
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"开始第 {attempt + 1} 次购票尝试")
                
                result = purchase_func(*args, **kwargs)
                
                if result:
                    self.logger.info(f"购票成功！(第 {attempt + 1} 次尝试)")
                    return True
                else:
                    # 购票失败，使用递增延迟
                    delay = base_delay + (attempt * 0.5)
                    self.logger.warning(
                        f"第 {attempt + 1} 次购票尝试失败，{delay} 秒后重试..."
                    )
                    time.sleep(delay)
                    
            except Exception as e:
                delay = base_delay + (attempt * 0.5)
                self.logger.error(
                    f"第 {attempt + 1} 次购票尝试出现异常: {e}, {delay} 秒后重试..."
                )
                time.sleep(delay)
        
        self.logger.error(f"购票失败，已达到最大重试次数 {max_retries}")
        return False
    
    def exponential_backoff_retry(self, 
                                 func: Callable, 
                                 *args, 
                                 max_retries: int = 5,
                                 base_delay: float = 1,
                                 max_delay: float = 60,
                                 **kwargs) -> Any:
        """
        指数退避重试机制
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            max_retries: 最大重试次数
            base_delay: 基础延迟时间
            max_delay: 最大延迟时间
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果
        """
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                result = func(*args, **kwargs)
                if attempt > 0:
                    self.logger.info(f"{func.__name__} 在第 {attempt + 1} 次尝试后成功")
                return result
                
            except Exception as e:
                last_exception = e
                if attempt < max_retries:
                    # 计算指数退避延迟
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    self.logger.warning(
                        f"{func.__name__} 第 {attempt + 1} 次尝试失败: {e}, "
                        f"{delay} 秒后重试..."
                    )
                    time.sleep(delay)
                else:
                    self.logger.error(
                        f"{func.__name__} 在 {max_retries + 1} 次尝试后仍然失败: {e}"
                    )
        
        raise last_exception
    
    def is_retryable_error(self, exception: Exception) -> bool:
        """
        判断异常是否可以重试
        
        Args:
            exception: 异常对象
            
        Returns:
            是否可以重试
        """
        retryable_exceptions = (
            TimeoutException,
            NoSuchElementException,
            ElementClickInterceptedException,
            StaleElementReferenceException,
            WebDriverException
        )
        
        # 检查异常类型
        if isinstance(exception, retryable_exceptions):
            return True
        
        # 检查异常消息中的关键词
        error_message = str(exception).lower()
        retryable_keywords = [
            'timeout',
            'not found',
            'not clickable',
            'stale element',
            'connection',
            'network',
            'server error'
        ]
        
        return any(keyword in error_message for keyword in retryable_keywords)
