"""
浏览器管理器模块
负责浏览器的初始化、配置和管理
"""

import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from typing import Optional
import time


class BrowserManager:
    """浏览器管理器类"""
    
    def __init__(self, config_manager):
        """
        初始化浏览器管理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config = config_manager
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        self.logger = logging.getLogger(__name__)
    
    def setup_browser(self) -> webdriver.Chrome:
        """
        设置并启动浏览器
        
        Returns:
            Chrome WebDriver实例
        """
        try:
            browser_settings = self.config.get_browser_settings()
            retry_settings = self.config.get_retry_settings()
            
            # 配置Chrome选项
            chrome_options = Options()
            
            # 移动端模拟设置
            mobile_emulation = {
                "deviceMetrics": {
                    "width": browser_settings.get('window_width', 375),
                    "height": browser_settings.get('window_height', 812),
                    "pixelRatio": 3.0
                },
                "userAgent": browser_settings.get('user_agent', 
                    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15")
            }
            chrome_options.add_experimental_option("mobileEmulation", mobile_emulation)
            
            # 其他浏览器设置
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 设置是否无头模式
            if browser_settings.get('headless', False):
                chrome_options.add_argument("--headless")
            
            # 自动下载并配置ChromeDriver
            self.logger.info("正在下载/配置ChromeDriver...")
            service = Service(ChromeDriverManager().install())

            # 启动浏览器
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置页面加载超时
            self.driver.set_page_load_timeout(retry_settings.get('page_load_timeout', 60))
            
            # 初始化等待对象
            self.wait = WebDriverWait(self.driver, retry_settings.get('element_wait_timeout', 30))
            
            self.logger.info("浏览器启动成功，已配置移动端模拟")
            return self.driver
            
        except WebDriverException as e:
            self.logger.error(f"浏览器启动失败: {e}")
            self.logger.error("可能的解决方案:")
            self.logger.error("1. 确保已安装Chrome浏览器")
            self.logger.error("2. 检查网络连接（需要下载ChromeDriver）")
            self.logger.error("3. 尝试更新Chrome浏览器到最新版本")
            raise
        except Exception as e:
            self.logger.error(f"浏览器启动时发生未知错误: {e}")
            self.logger.error("请检查系统环境和依赖包安装")
            raise
    
    def navigate_to_url(self, url: str, max_retries: int = 3) -> bool:
        """
        导航到指定URL
        
        Args:
            url: 目标URL
            max_retries: 最大重试次数
            
        Returns:
            是否成功导航
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"正在导航到: {url} (尝试 {attempt + 1}/{max_retries})")
                self.driver.get(url)
                
                # 等待页面加载完成
                self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")
                
                self.logger.info(f"成功导航到: {url}")
                return True
                
            except TimeoutException:
                self.logger.warning(f"导航超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
            except Exception as e:
                self.logger.error(f"导航失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
        
        self.logger.error(f"导航到 {url} 失败，已达到最大重试次数")
        return False
    
    def wait_for_element(self, selector: str, timeout: int = None) -> bool:
        """
        等待元素出现
        
        Args:
            selector: CSS选择器
            timeout: 超时时间
            
        Returns:
            元素是否出现
        """
        try:
            if timeout is None:
                timeout = self.config.get('retry_settings.element_wait_timeout', 30)
            
            wait = WebDriverWait(self.driver, timeout)
            wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
            return True
        except TimeoutException:
            self.logger.warning(f"等待元素超时: {selector}")
            return False
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("浏览器已关闭")
            except Exception as e:
                self.logger.error(f"关闭浏览器时出错: {e}")
    
    def get_current_url(self) -> str:
        """获取当前URL"""
        return self.driver.current_url if self.driver else ""
    
    def take_screenshot(self, filename: str = None) -> str:
        """
        截取屏幕截图
        
        Args:
            filename: 文件名，如果为None则自动生成
            
        Returns:
            截图文件路径
        """
        if not filename:
            timestamp = int(time.time())
            filename = f"screenshot_{timestamp}.png"
        
        try:
            self.driver.save_screenshot(filename)
            self.logger.info(f"截图已保存: {filename}")
            return filename
        except Exception as e:
            self.logger.error(f"截图失败: {e}")
            return ""
