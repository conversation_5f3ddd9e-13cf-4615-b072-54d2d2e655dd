"""
自动化抢票软件主控制器
整合所有模块，实现完整的抢票流程
"""

import logging
import time
import sys
from typing import Optional

from config_manager import ConfigManager
from browser_manager import BrowserManager
from page_operations import PageOperations
from retry_manager import RetryManager


class TicketBot:
    """自动化抢票机器人主类"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化抢票机器人
        
        Args:
            config_file: 配置文件路径
        """
        try:
            # 初始化各个管理器
            self.config_manager = ConfigManager(config_file)
            self.browser_manager = BrowserManager(self.config_manager)
            self.retry_manager = RetryManager(self.config_manager)
            self.page_operations: Optional[PageOperations] = None
            
            # 设置日志
            self.logger = logging.getLogger(__name__)
            self.logger.info("抢票机器人初始化完成")
            
        except Exception as e:
            print(f"初始化失败: {e}")
            sys.exit(1)
    
    def start_ticket_purchase(self) -> bool:
        """
        开始抢票流程
        
        Returns:
            是否抢票成功
        """
        try:
            self.logger.info("=" * 50)
            self.logger.info("开始自动化抢票流程")
            self.logger.info("=" * 50)
            
            # 1. 启动浏览器
            if not self._setup_browser():
                return False
            
            # 2. 导航到登录页面并等待用户登录
            if not self._handle_login_process():
                return False
            
            # 3. 导航到演唱会详情页
            if not self._navigate_to_concert_page():
                return False
            
            # 4. 执行购票流程
            success = self.retry_manager.smart_retry_purchase(self._execute_purchase_flow)
            
            if success:
                self.logger.info("🎉 抢票成功！")
                self._take_success_screenshot()
            else:
                self.logger.error("❌ 抢票失败")
                self._take_failure_screenshot()
            
            return success
            
        except KeyboardInterrupt:
            self.logger.info("用户中断了抢票流程")
            return False
        except Exception as e:
            self.logger.error(f"抢票流程出现异常: {e}")
            self._take_failure_screenshot()
            return False
        finally:
            self._cleanup()
    
    def _setup_browser(self) -> bool:
        """
        设置浏览器
        
        Returns:
            是否设置成功
        """
        try:
            self.logger.info("正在启动浏览器...")
            self.browser_manager.setup_browser()
            self.page_operations = PageOperations(self.browser_manager, self.config_manager)
            self.logger.info("浏览器启动成功")
            return True
        except Exception as e:
            self.logger.error(f"浏览器启动失败: {e}")
            return False
    
    def _handle_login_process(self) -> bool:
        """
        处理登录流程
        
        Returns:
            是否登录成功
        """
        try:
            login_url = self.config_manager.get('urls.login_url')
            self.logger.info(f"正在导航到登录页面: {login_url}")
            
            # 导航到登录页面
            if not self.browser_manager.navigate_to_url(login_url):
                self.logger.error("导航到登录页面失败")
                return False
            
            # 等待用户手动完成登录
            self.logger.info("请在浏览器中手动完成登录...")
            if not self.page_operations.wait_for_login_completion():
                self.logger.error("登录超时或失败")
                return False
            
            self.logger.info("用户登录成功")
            return True
            
        except Exception as e:
            self.logger.error(f"登录流程失败: {e}")
            return False
    
    def _navigate_to_concert_page(self) -> bool:
        """
        导航到演唱会详情页
        
        Returns:
            是否导航成功
        """
        try:
            concert_url = self.config_manager.get('urls.concert_detail_url')
            self.logger.info(f"正在导航到演唱会详情页: {concert_url}")
            
            if not self.browser_manager.navigate_to_url(concert_url):
                self.logger.error("导航到演唱会详情页失败")
                return False
            
            # 等待页面加载
            time.sleep(3)
            self.logger.info("成功到达演唱会详情页")
            return True
            
        except Exception as e:
            self.logger.error(f"导航到演唱会详情页失败: {e}")
            return False
    
    def _execute_purchase_flow(self) -> bool:
        """
        执行购票流程
        
        Returns:
            是否购票成功
        """
        try:
            target_selection = self.config_manager.get_target_selection()
            
            # 1. 选择场次
            self.logger.info("步骤 1: 选择演出场次")
            if not self.page_operations.select_session(target_selection.get('session_text')):
                self.logger.error("选择场次失败")
                return False
            
            time.sleep(1)
            
            # 2. 选择票档
            self.logger.info("步骤 2: 选择票档")
            if not self.page_operations.select_ticket_tier(target_selection.get('ticket_tier_text')):
                self.logger.error("选择票档失败")
                return False
            
            time.sleep(1)
            
            # 3. 调整数量
            self.logger.info("步骤 3: 调整票数")
            if not self.page_operations.adjust_quantity(target_selection.get('quantity')):
                self.logger.error("调整票数失败")
                return False
            
            time.sleep(1)
            
            # 4. 点击立即购买
            self.logger.info("步骤 4: 点击立即购买")
            if not self.page_operations.click_buy_now_button():
                self.logger.error("点击立即购买失败")
                return False
            
            # 5. 等待页面跳转并处理支付确认
            time.sleep(3)
            return self._handle_payment_confirmation()
            
        except Exception as e:
            self.logger.error(f"购票流程执行失败: {e}")
            return False
    
    def _handle_payment_confirmation(self) -> bool:
        """
        处理支付确认流程
        
        Returns:
            是否处理成功
        """
        try:
            # 检查当前页面状态
            page_status = self.page_operations.check_page_status()
            self.logger.info(f"当前页面状态: {page_status}")
            
            if page_status in ["payment_page", "confirmation_page"]:
                self.logger.info("步骤 5: 处理支付确认")
                return self.page_operations.handle_payment_confirmation()
            elif page_status == "success_page":
                self.logger.info("直接跳转到成功页面")
                return True
            else:
                self.logger.warning(f"未知页面状态: {page_status}")
                # 尝试处理支付确认
                return self.page_operations.handle_payment_confirmation()
                
        except Exception as e:
            self.logger.error(f"支付确认处理失败: {e}")
            return False
    
    def _take_success_screenshot(self):
        """截取成功截图"""
        try:
            filename = f"success_{int(time.time())}.png"
            self.browser_manager.take_screenshot(filename)
            self.logger.info(f"成功截图已保存: {filename}")
        except Exception as e:
            self.logger.error(f"截取成功截图失败: {e}")
    
    def _take_failure_screenshot(self):
        """截取失败截图"""
        try:
            filename = f"failure_{int(time.time())}.png"
            self.browser_manager.take_screenshot(filename)
            self.logger.info(f"失败截图已保存: {filename}")
        except Exception as e:
            self.logger.error(f"截取失败截图失败: {e}")
    
    def _cleanup(self):
        """清理资源"""
        try:
            if self.browser_manager:
                self.browser_manager.close_browser()
            self.logger.info("资源清理完成")
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")


def main():
    """主函数"""
    print("🎫 自动化抢票软件启动中...")
    print("请确保已经安装了Chrome浏览器和ChromeDriver")
    print("=" * 50)
    
    # 创建抢票机器人实例
    bot = TicketBot()
    
    # 开始抢票
    success = bot.start_ticket_purchase()
    
    if success:
        print("\n🎉 抢票成功！请查看浏览器确认订单状态。")
    else:
        print("\n❌ 抢票失败，请查看日志文件了解详细信息。")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
