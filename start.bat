@echo off
chcp 65001 >nul
title 自动化抢票软件

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎫 自动化抢票软件启动器                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
echo.

REM 检查依赖是否安装
echo 📦 检查依赖包...
pip show selenium >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖包安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo ✅ 依赖包已安装
echo.

REM 检查配置文件
if not exist "config.json" (
    echo ❌ 错误: 配置文件 config.json 不存在
    echo 请确保配置文件存在并正确配置
    pause
    exit /b 1
)

echo ✅ 配置文件存在
echo.

REM 提供选项菜单
:menu
echo 请选择操作:
echo 1. 运行环境测试
echo 2. 开始抢票
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 🧪 正在运行环境测试...
    python test_environment.py
    echo.
    goto menu
) else if "%choice%"=="2" (
    echo.
    echo 🚀 正在启动抢票程序...
    python run.py
    goto end
) else if "%choice%"=="3" (
    goto end
) else (
    echo ❌ 无效选择，请重新输入
    echo.
    goto menu
)

:end
echo.
echo 程序已结束
pause
