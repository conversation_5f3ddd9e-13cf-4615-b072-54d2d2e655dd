"""
页面操作器模块
封装各个页面的具体操作逻辑
"""

import logging
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.action_chains import ActionChains


class PageOperations:
    """页面操作器类"""
    
    def __init__(self, browser_manager, config_manager):
        """
        初始化页面操作器
        
        Args:
            browser_manager: 浏览器管理器实例
            config_manager: 配置管理器实例
        """
        self.browser = browser_manager
        self.config = config_manager
        self.driver = browser_manager.driver
        self.wait = browser_manager.wait
        self.logger = logging.getLogger(__name__)
    
    def wait_for_login_completion(self, timeout: int = 300) -> bool:
        """
        等待用户完成登录
        
        Args:
            timeout: 等待超时时间（秒）
            
        Returns:
            是否登录成功
        """
        self.logger.info("等待用户手动完成登录...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_url = self.driver.current_url
            
            # 检查是否已经离开登录页面
            if "login" not in current_url.lower():
                self.logger.info("检测到用户已完成登录")
                return True
            
            # 检查是否有登录成功的标识
            try:
                # 可以根据实际情况添加更多登录成功的判断条件
                if self.driver.find_elements(By.CSS_SELECTOR, "[class*='user'], [class*='profile'], [class*='avatar']"):
                    self.logger.info("检测到登录成功标识")
                    return True
            except:
                pass
            
            time.sleep(2)
        
        self.logger.warning("等待登录超时")
        return False
    
    def select_session(self, target_session_text: str) -> bool:
        """
        选择演出场次
        
        Args:
            target_session_text: 目标场次文本
            
        Returns:
            是否选择成功
        """
        try:
            self.logger.info(f"正在选择场次: {target_session_text}")
            
            # 等待场次列表加载
            session_selector = self.config.get('selectors.session_selector')
            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, session_selector)))
            
            # 查找所有场次元素
            session_elements = self.driver.find_elements(By.CSS_SELECTOR, session_selector)
            
            for element in session_elements:
                if target_session_text in element.text:
                    # 滚动到元素可见
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    
                    # 点击场次
                    element.click()
                    self.logger.info(f"成功选择场次: {target_session_text}")
                    return True
            
            self.logger.error(f"未找到目标场次: {target_session_text}")
            return False
            
        except Exception as e:
            self.logger.error(f"选择场次失败: {e}")
            return False
    
    def select_ticket_tier(self, target_tier_text: str) -> bool:
        """
        选择票档
        
        Args:
            target_tier_text: 目标票档文本
            
        Returns:
            是否选择成功
        """
        try:
            self.logger.info(f"正在选择票档: {target_tier_text}")
            
            # 等待票档列表加载
            tier_selector = self.config.get('selectors.ticket_tier_selector')
            self.wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, tier_selector)))
            
            # 查找所有票档元素
            tier_elements = self.driver.find_elements(By.CSS_SELECTOR, tier_selector)
            
            for element in tier_elements:
                if target_tier_text in element.text:
                    # 滚动到元素可见
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                    time.sleep(1)
                    
                    # 点击票档
                    element.click()
                    self.logger.info(f"成功选择票档: {target_tier_text}")
                    return True
            
            self.logger.error(f"未找到目标票档: {target_tier_text}")
            return False
            
        except Exception as e:
            self.logger.error(f"选择票档失败: {e}")
            return False
    
    def adjust_quantity(self, target_quantity: int) -> bool:
        """
        调整票数
        
        Args:
            target_quantity: 目标数量
            
        Returns:
            是否调整成功
        """
        try:
            self.logger.info(f"正在调整票数到: {target_quantity}")
            
            # 获取选择器
            increase_selector = self.config.get('selectors.quantity_increase_selector')
            decrease_selector = self.config.get('selectors.quantity_decrease_selector')
            display_selector = self.config.get('selectors.quantity_display_selector')
            
            max_attempts = 20  # 防止无限循环
            
            for attempt in range(max_attempts):
                try:
                    # 获取当前数量
                    quantity_element = self.driver.find_element(By.CSS_SELECTOR, display_selector)
                    current_quantity = int(quantity_element.text.strip())
                    
                    self.logger.info(f"当前数量: {current_quantity}, 目标数量: {target_quantity}")
                    
                    if current_quantity == target_quantity:
                        self.logger.info("数量调整完成")
                        return True
                    elif current_quantity < target_quantity:
                        # 需要增加
                        increase_button = self.driver.find_element(By.CSS_SELECTOR, increase_selector)
                        increase_button.click()
                        self.logger.info("点击增加按钮")
                    else:
                        # 需要减少
                        decrease_button = self.driver.find_element(By.CSS_SELECTOR, decrease_selector)
                        decrease_button.click()
                        self.logger.info("点击减少按钮")
                    
                    time.sleep(0.5)  # 等待UI更新
                    
                except (NoSuchElementException, ValueError) as e:
                    self.logger.warning(f"调整数量时出错: {e}")
                    time.sleep(1)
                    continue
            
            self.logger.error("调整数量失败，达到最大尝试次数")
            return False
            
        except Exception as e:
            self.logger.error(f"调整数量失败: {e}")
            return False
    
    def click_buy_now_button(self) -> bool:
        """
        点击立即购买按钮
        
        Returns:
            是否点击成功
        """
        try:
            self.logger.info("正在点击立即购买按钮")
            
            # 尝试多种可能的选择器
            possible_selectors = [
                self.config.get('selectors.buy_now_button_selector'),
                "button:contains('立即购买')",
                "[class*='buy']",
                "[class*='purchase']",
                "button[class*='submit']",
                ".buy-button",
                "#buyNow"
            ]
            
            for selector in possible_selectors:
                try:
                    # 等待按钮可点击
                    button = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                    
                    # 滚动到按钮可见
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)
                    
                    # 点击按钮
                    button.click()
                    self.logger.info("成功点击立即购买按钮")
                    return True
                    
                except (TimeoutException, NoSuchElementException):
                    continue
                except ElementClickInterceptedException:
                    # 尝试使用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", button)
                    self.logger.info("使用JavaScript成功点击立即购买按钮")
                    return True
            
            self.logger.error("未找到立即购买按钮")
            return False
            
        except Exception as e:
            self.logger.error(f"点击立即购买按钮失败: {e}")
            return False

    def handle_payment_confirmation(self) -> bool:
        """
        处理支付确认页面

        Returns:
            是否处理成功
        """
        try:
            self.logger.info("正在处理支付确认页面")

            # 等待页面加载
            time.sleep(3)

            # 勾选条款复选框
            if not self._check_terms_checkbox():
                self.logger.error("勾选条款复选框失败")
                return False

            # 点击确认按钮
            if not self._click_confirm_button():
                self.logger.error("点击确认按钮失败")
                return False

            self.logger.info("支付确认处理完成")
            return True

        except Exception as e:
            self.logger.error(f"处理支付确认失败: {e}")
            return False

    def _check_terms_checkbox(self) -> bool:
        """
        勾选条款复选框

        Returns:
            是否勾选成功
        """
        try:
            # 尝试多种可能的选择器
            possible_selectors = [
                self.config.get('selectors.terms_checkbox_selector'),
                "input[type='checkbox']",
                "[role='checkbox']",
                ".checkbox",
                "[class*='agree']",
                "[class*='terms']"
            ]

            for selector in possible_selectors:
                try:
                    checkbox = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))

                    # 检查是否已经勾选
                    if not checkbox.is_selected():
                        checkbox.click()
                        self.logger.info("成功勾选条款复选框")
                    else:
                        self.logger.info("条款复选框已经勾选")

                    return True

                except (TimeoutException, NoSuchElementException):
                    continue

            self.logger.warning("未找到条款复选框，可能不需要勾选")
            return True

        except Exception as e:
            self.logger.error(f"勾选条款复选框失败: {e}")
            return False

    def _click_confirm_button(self) -> bool:
        """
        点击确认按钮

        Returns:
            是否点击成功
        """
        try:
            # 尝试多种可能的选择器
            possible_selectors = [
                self.config.get('selectors.confirm_button_selector'),
                "button:contains('确认')",
                "[class*='confirm']",
                "[class*='submit']",
                "button[type='submit']",
                ".confirm-button"
            ]

            for selector in possible_selectors:
                try:
                    button = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))

                    # 滚动到按钮可见
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)

                    # 点击按钮
                    button.click()
                    self.logger.info("成功点击确认按钮")
                    return True

                except (TimeoutException, NoSuchElementException):
                    continue
                except ElementClickInterceptedException:
                    # 尝试使用JavaScript点击
                    self.driver.execute_script("arguments[0].click();", button)
                    self.logger.info("使用JavaScript成功点击确认按钮")
                    return True

            self.logger.error("未找到确认按钮")
            return False

        except Exception as e:
            self.logger.error(f"点击确认按钮失败: {e}")
            return False

    def check_page_status(self) -> str:
        """
        检查当前页面状态

        Returns:
            页面状态描述
        """
        try:
            current_url = self.driver.current_url
            page_title = self.driver.title

            # 检查是否在登录页面
            if "login" in current_url.lower():
                return "login_page"

            # 检查是否在详情页面
            if "detail" in current_url.lower() or "selectTicket" in current_url:
                return "detail_page"

            # 检查是否在支付页面
            if "payment" in current_url.lower() or "pay" in current_url.lower():
                return "payment_page"

            # 检查是否在确认页面
            if "confirm" in current_url.lower() or "order" in current_url.lower():
                return "confirmation_page"

            # 检查是否有成功标识
            success_indicators = ["success", "complete", "done", "finish"]
            if any(indicator in current_url.lower() or indicator in page_title.lower()
                   for indicator in success_indicators):
                return "success_page"

            return "unknown_page"

        except Exception as e:
            self.logger.error(f"检查页面状态失败: {e}")
            return "error_page"
