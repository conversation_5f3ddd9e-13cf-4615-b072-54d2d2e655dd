# 🎫 自动化抢票软件 - 技术设计文档

## 📋 项目概述

本项目是一个针对Galaxy Ticketing网站的自动化抢票软件，采用模块化设计，支持移动端访问模拟，具备完整的购票流程和智能重试机制。

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    主控制器 (TicketBot)                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌──────┐ │
│  │配置管理器    │  │浏览器管理器  │  │页面操作器    │  │重试  │ │
│  │ConfigManager│  │BrowserMgr   │  │PageOps      │  │Mgr   │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └──────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Selenium WebDriver                       │
├─────────────────────────────────────────────────────────────┤
│                    Chrome Browser                           │
└─────────────────────────────────────────────────────────────┘
```

### 核心模块设计

#### 1. 配置管理器 (ConfigManager)
**职责**: 管理所有配置参数，提供统一的配置访问接口

**主要功能**:
- 加载和解析JSON配置文件
- 提供类型安全的配置访问方法
- 支持配置的动态更新和保存
- 设置日志系统配置

**关键方法**:
```python
def get(key: str, default=None) -> Any
def get_urls() -> Dict[str, str]
def get_target_selection() -> Dict[str, Any]
def get_browser_settings() -> Dict[str, Any]
def get_retry_settings() -> Dict[str, Any]
def get_selectors() -> Dict[str, str]
```

#### 2. 浏览器管理器 (BrowserManager)
**职责**: 管理浏览器的生命周期和配置

**主要功能**:
- 初始化Chrome浏览器并配置移动端模拟
- 管理页面导航和加载
- 提供元素等待和截图功能
- 处理浏览器异常和清理

**移动端模拟配置**:
```python
mobile_emulation = {
    "deviceMetrics": {
        "width": 375,
        "height": 812,
        "pixelRatio": 3.0
    },
    "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)..."
}
```

**关键方法**:
```python
def setup_browser() -> webdriver.Chrome
def navigate_to_url(url: str, max_retries: int = 3) -> bool
def wait_for_element(selector: str, timeout: int = None) -> bool
def take_screenshot(filename: str = None) -> str
def close_browser()
```

#### 3. 页面操作器 (PageOperations)
**职责**: 封装具体的页面交互逻辑

**主要功能**:
- 等待用户完成登录
- 选择演出场次和票档
- 调整票数数量
- 处理购买和支付确认流程
- 检查页面状态

**核心操作流程**:
```python
def wait_for_login_completion(timeout: int = 300) -> bool
def select_session(target_session_text: str) -> bool
def select_ticket_tier(target_tier_text: str) -> bool
def adjust_quantity(target_quantity: int) -> bool
def click_buy_now_button() -> bool
def handle_payment_confirmation() -> bool
```

#### 4. 重试管理器 (RetryManager)
**职责**: 实现智能重试机制和错误处理

**主要功能**:
- 提供装饰器模式的重试机制
- 实现指数退避算法
- 针对购票流程的特殊重试逻辑
- 异常分类和处理

**重试策略**:
- **基础重试**: 固定间隔重试
- **智能重试**: 递增延迟重试
- **指数退避**: 指数增长延迟重试
- **购票专用**: 针对购票流程优化的重试

**关键方法**:
```python
def retry_on_failure(max_retries, delay, exceptions) -> decorator
def execute_with_retry(func, *args, **kwargs) -> Any
def smart_retry_purchase(purchase_func, *args, **kwargs) -> bool
def exponential_backoff_retry(func, *args, **kwargs) -> Any
```

## 🔧 技术实现细节

### 移动端模拟策略

#### 1. User-Agent模拟
使用真实的iPhone Safari User-Agent字符串：
```
Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) 
AppleWebKit/605.1.15 (KHTML, like Gecko) 
Version/14.1.2 Mobile/15E148 Safari/604.1
```

#### 2. 视口配置
模拟iPhone 12 Pro的屏幕尺寸：
- 宽度: 375px
- 高度: 812px
- 像素比: 3.0

#### 3. 反检测机制
```python
# 移除webdriver标识
driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

# 禁用自动化扩展
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)
```

### 元素定位策略

#### 1. 多重选择器策略
为每个关键元素提供多个备选选择器：
```python
possible_selectors = [
    self.config.get('selectors.buy_now_button_selector'),
    "button:contains('立即购买')",
    "[class*='buy']",
    "[class*='purchase']",
    "button[class*='submit']"
]
```

#### 2. 智能等待机制
```python
# 显式等待元素可点击
button = self.wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))

# 滚动到元素可见
self.driver.execute_script("arguments[0].scrollIntoView(true);", button)

# 处理点击拦截
try:
    button.click()
except ElementClickInterceptedException:
    self.driver.execute_script("arguments[0].click();", button)
```

### 错误处理和重试机制

#### 1. 异常分类
```python
retryable_exceptions = (
    TimeoutException,           # 超时异常
    NoSuchElementException,     # 元素未找到
    ElementClickInterceptedException,  # 点击被拦截
    StaleElementReferenceException,    # 元素引用失效
    WebDriverException          # WebDriver异常
)
```

#### 2. 重试策略
- **立即重试**: 网络临时问题
- **延迟重试**: 页面加载问题
- **递增延迟**: 服务器压力问题
- **指数退避**: 严重错误情况

#### 3. 购票专用重试
```python
def smart_retry_purchase(self, purchase_func, *args, **kwargs):
    for attempt in range(max_retries):
        # 使用递增延迟策略
        delay = base_delay + (attempt * 0.5)
        # 执行购票逻辑
        # 处理结果和异常
```

## 📊 数据流设计

### 配置数据流
```
config.json → ConfigManager → 各个模块
```

### 操作数据流
```
用户输入 → TicketBot → PageOperations → Selenium → 网页
```

### 日志数据流
```
各模块 → Python logging → 文件 + 控制台
```

## 🔒 安全性考虑

### 1. 反检测措施
- 移除WebDriver标识
- 使用真实的移动端User-Agent
- 模拟真实的设备指纹
- 随机化操作间隔

### 2. 频率控制
- 配置最大重试次数
- 实现智能延迟机制
- 避免过于频繁的请求

### 3. 错误处理
- 完整的异常捕获和处理
- 详细的日志记录
- 自动截图保存证据

## 🚀 性能优化

### 1. 浏览器优化
```python
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--disable-blink-features=AutomationControlled")
```

### 2. 等待优化
- 使用显式等待替代隐式等待
- 合理设置超时时间
- 避免不必要的sleep

### 3. 内存管理
- 及时清理浏览器资源
- 避免内存泄漏
- 合理的异常处理

## 📈 扩展性设计

### 1. 模块化架构
每个模块职责单一，便于独立测试和维护

### 2. 配置驱动
所有关键参数都可通过配置文件修改

### 3. 插件化支持
可以轻松添加新的页面操作或重试策略

### 4. 多站点支持
架构设计支持扩展到其他票务网站

## 🧪 测试策略

### 1. 单元测试
- 配置管理器测试
- 重试机制测试
- 工具函数测试

### 2. 集成测试
- 浏览器启动测试
- 页面导航测试
- 元素定位测试

### 3. 端到端测试
- 完整流程测试
- 异常场景测试
- 性能压力测试

## 📝 维护指南

### 1. 配置更新
当网站结构变化时，主要需要更新：
- CSS选择器
- 页面URL
- 元素等待时间

### 2. 代码维护
- 定期更新依赖包
- 监控浏览器版本兼容性
- 优化重试策略

### 3. 监控和调试
- 查看日志文件
- 分析截图信息
- 监控成功率统计
