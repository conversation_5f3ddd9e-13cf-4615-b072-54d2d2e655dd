#!/usr/bin/env python3
"""
环境测试脚本
用于验证系统环境和配置是否正确
"""

import sys
import os
import json
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import WebDriverException


def test_python_version():
    """测试Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro} (符合要求)")
        return True
    else:
        print(f"❌ Python版本: {version.major}.{version.minor}.{version.micro} (需要3.7+)")
        return False


def test_required_packages():
    """测试必需的包"""
    print("\n📦 检查必需的包...")
    required_packages = [
        'selenium',
        'json',
        'logging'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
        return False
    return True


def test_chrome_browser():
    """测试Chrome浏览器"""
    print("\n🌐 检查Chrome浏览器...")
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(options=chrome_options)
        version = driver.capabilities['browserVersion']
        print(f"✅ Chrome浏览器版本: {version}")
        driver.quit()
        return True
        
    except WebDriverException as e:
        print(f"❌ Chrome浏览器测试失败: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False


def test_config_file():
    """测试配置文件"""
    print("\n⚙️ 检查配置文件...")
    config_file = "config.json"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"✅ 配置文件格式正确")
        
        # 检查必要的配置项
        required_sections = [
            'urls',
            'target_selection',
            'browser_settings',
            'retry_settings',
            'selectors'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in config:
                missing_sections.append(section)
            else:
                print(f"✅ 配置节 '{section}': 存在")
        
        if missing_sections:
            print(f"❌ 缺失配置节: {missing_sections}")
            return False
        
        # 检查关键配置项
        critical_configs = [
            ('urls', 'concert_detail_url'),
            ('target_selection', 'session_text'),
            ('target_selection', 'ticket_tier_text'),
            ('target_selection', 'quantity')
        ]
        
        for section, key in critical_configs:
            if key not in config[section]:
                print(f"❌ 缺失关键配置: {section}.{key}")
                return False
            else:
                value = config[section][key]
                print(f"✅ {section}.{key}: {value}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False


def test_mobile_simulation():
    """测试移动端模拟"""
    print("\n📱 测试移动端模拟...")
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        
        # 移动端模拟配置
        mobile_emulation = {
            "deviceMetrics": {
                "width": 375,
                "height": 812,
                "pixelRatio": 3.0
            },
            "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15"
        }
        chrome_options.add_experimental_option("mobileEmulation", mobile_emulation)
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # 检查User-Agent
        user_agent = driver.execute_script("return navigator.userAgent;")
        if "iPhone" in user_agent:
            print("✅ 移动端User-Agent设置成功")
        else:
            print(f"❌ 移动端User-Agent设置失败: {user_agent}")
            driver.quit()
            return False
        
        # 检查视口大小
        viewport_width = driver.execute_script("return window.innerWidth;")
        viewport_height = driver.execute_script("return window.innerHeight;")
        print(f"✅ 视口大小: {viewport_width}x{viewport_height}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ 移动端模拟测试失败: {e}")
        return False


def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        
        driver = webdriver.Chrome(options=chrome_options)
        
        # 测试访问目标网站
        test_url = "https://www.galaxyticketing.com"
        driver.get(test_url)
        
        if "galaxy" in driver.title.lower() or "galaxy" in driver.current_url.lower():
            print(f"✅ 网络连接正常，可以访问目标网站")
        else:
            print(f"⚠️ 可以访问网站，但页面内容可能不正确")
            print(f"页面标题: {driver.title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ 网络连接测试失败: {e}")
        return False


def test_logging_system():
    """测试日志系统"""
    print("\n📝 测试日志系统...")
    try:
        # 配置测试日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('test.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logger = logging.getLogger('test')
        logger.info("这是一条测试日志")
        
        # 检查日志文件是否创建
        if os.path.exists('test.log'):
            print("✅ 日志系统工作正常")
            os.remove('test.log')  # 清理测试文件
            return True
        else:
            print("❌ 日志文件创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 环境测试开始")
    print("=" * 60)
    
    tests = [
        ("Python版本", test_python_version),
        ("必需包", test_required_packages),
        ("Chrome浏览器", test_chrome_browser),
        ("配置文件", test_config_file),
        ("移动端模拟", test_mobile_simulation),
        ("网络连接", test_network_connectivity),
        ("日志系统", test_logging_system)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果摘要
    print("\n" + "=" * 60)
    print("📊 测试结果摘要:")
    print("-" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！环境配置正确，可以开始使用抢票软件。")
        return True
    else:
        print(f"\n⚠️ 有 {total - passed} 项测试失败，请根据上述信息修复问题。")
        return False


if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
