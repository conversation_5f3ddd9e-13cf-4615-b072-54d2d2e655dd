# 🎫 自动化抢票软件

一个针对Galaxy Ticketing网站的自动化抢票软件，支持移动端访问模拟，具备完整的购票流程和智能重试机制。

## ✨ 功能特性

- 🔐 **智能登录等待**: 等待用户手动完成登录，避免验证码问题
- 📱 **移动端模拟**: 完美模拟iPhone移动端环境，规避访问限制
- 🎯 **精准选择**: 自动选择指定场次、票档和数量
- 🔄 **智能重试**: 多层次重试机制，提高抢票成功率
- 📸 **自动截图**: 成功/失败时自动截图保存证据
- 📝 **详细日志**: 完整记录操作过程，便于问题诊断
- ⚙️ **灵活配置**: 所有参数可通过配置文件轻松修改

## 🛠️ 技术架构

### 核心模块
- **主控制器** (`ticket_bot.py`): 协调整个抢票流程
- **浏览器管理器** (`browser_manager.py`): 处理浏览器初始化和移动端模拟
- **页面操作器** (`page_operations.py`): 封装各页面的具体操作
- **重试管理器** (`retry_manager.py`): 实现智能重试逻辑
- **配置管理器** (`config_manager.py`): 管理所有可配置参数

### 技术栈
- **Selenium WebDriver**: 浏览器自动化
- **Chrome/Chromium**: 目标浏览器
- **Python 3.7+**: 开发语言
- **JSON**: 配置文件格式

## 📋 系统要求

- Python 3.7 或更高版本
- Chrome 浏览器 (最新版本)
- ChromeDriver (自动下载)
- Windows/macOS/Linux 操作系统

## 🚀 安装步骤

### 1. 克隆或下载项目
```bash
git clone <repository-url>
cd ticket_bot
```

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 3. 安装Chrome浏览器
确保系统已安装最新版本的Chrome浏览器。

### 4. 配置参数
编辑 `config.json` 文件，修改以下关键参数：

```json
{
  "urls": {
    "concert_detail_url": "你的演唱会详情页URL"
  },
  "target_selection": {
    "session_text": "目标场次文本",
    "ticket_tier_text": "目标票档文本",
    "quantity": 目标数量
  }
}
```

## 🎯 使用方法

### 基本使用
```bash
python ticket_bot.py
```

### 抢票流程
1. **启动程序**: 运行上述命令
2. **浏览器打开**: 程序自动打开Chrome浏览器并导航到登录页面
3. **手动登录**: 在浏览器中手动完成登录（包括验证码等）
4. **自动抢票**: 登录完成后，程序自动执行抢票流程
5. **查看结果**: 程序会显示抢票结果并保存截图

## ⚙️ 配置说明

### 主要配置项

#### URLs配置
```json
"urls": {
  "login_url": "登录页面URL",
  "concert_detail_url": "演唱会详情页URL"
}
```

#### 目标选择配置
```json
"target_selection": {
  "session_text": "2025-04-12 星期六 20:00",
  "ticket_tier_text": "Reserved B(MOP 588.00)",
  "quantity": 4
}
```

#### 浏览器设置
```json
"browser_settings": {
  "headless": false,
  "window_width": 375,
  "window_height": 812,
  "user_agent": "iPhone用户代理字符串"
}
```

#### 重试设置
```json
"retry_settings": {
  "max_retries": 10,
  "retry_delay": 2,
  "element_wait_timeout": 30,
  "page_load_timeout": 60
}
```

### 选择器配置
程序使用CSS选择器定位页面元素，可根据网站变化调整：

```json
"selectors": {
  "session_selector": "div.sessionList.fouceStyle.font-bold span",
  "ticket_tier_selector": "span.ticketText___cw4vv",
  "quantity_increase_selector": "span[role='img'] svg use[xlink:href='#iconzengjia']",
  "buy_now_button_selector": "button:contains('立即购买')"
}
```

## 📊 日志和监控

### 日志文件
- **位置**: `ticket_bot.log`
- **内容**: 详细的操作记录和错误信息
- **格式**: 时间戳 + 日志级别 + 消息内容

### 截图文件
- **成功截图**: `success_<timestamp>.png`
- **失败截图**: `failure_<timestamp>.png`
- **调试截图**: `screenshot_<timestamp>.png`

## 🔧 故障排除

### 常见问题

#### 1. ChromeDriver版本不匹配
**症状**: 浏览器启动失败
**解决**: 更新Chrome浏览器到最新版本，或手动下载匹配的ChromeDriver

#### 2. 元素定位失败
**症状**: 找不到页面元素
**解决**: 检查并更新`config.json`中的选择器配置

#### 3. 登录超时
**症状**: 等待登录时间过长
**解决**: 增加`retry_settings.element_wait_timeout`的值

#### 4. 网络连接问题
**症状**: 页面加载失败
**解决**: 检查网络连接，增加`retry_settings.page_load_timeout`的值

### 调试技巧

1. **启用详细日志**: 将`logging.level`设置为`DEBUG`
2. **禁用无头模式**: 将`browser_settings.headless`设置为`false`
3. **增加等待时间**: 适当增加各种超时设置
4. **查看截图**: 检查自动保存的截图文件

## ⚠️ 注意事项

1. **合法使用**: 请确保使用本软件符合相关网站的服务条款
2. **网络环境**: 建议在稳定的网络环境下使用
3. **浏览器版本**: 保持Chrome浏览器为最新版本
4. **配置更新**: 网站更新时可能需要调整选择器配置
5. **频率控制**: 避免过于频繁的请求，以免被网站限制

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件 `ticket_bot.log`
2. 检查截图文件了解当前状态
3. 确认配置文件设置正确
4. 更新浏览器和依赖包到最新版本

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站服务条款。