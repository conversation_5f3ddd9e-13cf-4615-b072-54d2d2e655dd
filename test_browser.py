#!/usr/bin/env python3
"""
浏览器测试脚本
用于快速测试浏览器启动是否正常
"""

import sys
import logging
from config_manager import ConfigManager
from browser_manager import <PERSON>rowserManager

def test_browser_startup():
    """测试浏览器启动"""
    print("🧪 测试浏览器启动...")
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化浏览器管理器
        browser_manager = BrowserManager(config_manager)
        
        # 启动浏览器
        print("正在启动浏览器...")
        driver = browser_manager.setup_browser()
        
        if driver:
            print("✅ 浏览器启动成功！")
            
            # 测试导航
            print("测试导航到百度...")
            success = browser_manager.navigate_to_url("https://www.baidu.com")
            
            if success:
                print("✅ 页面导航成功！")
                print(f"当前页面标题: {driver.title}")
                print(f"当前URL: {driver.current_url}")
                
                # 截图测试
                screenshot_path = browser_manager.take_screenshot("test_screenshot.png")
                if screenshot_path:
                    print(f"✅ 截图成功: {screenshot_path}")
                
            else:
                print("❌ 页面导航失败")
            
            # 等待用户查看
            input("\n按回车键关闭浏览器...")
            
            # 关闭浏览器
            browser_manager.close_browser()
            print("✅ 浏览器已关闭")
            
            return True
        else:
            print("❌ 浏览器启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎫 浏览器启动测试")
    print("=" * 50)
    
    # 设置简单的日志配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    success = test_browser_startup()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 浏览器测试通过！可以正常使用抢票软件。")
    else:
        print("❌ 浏览器测试失败，请检查环境配置。")
        print("\n可能的解决方案:")
        print("1. 确保已安装Chrome浏览器")
        print("2. 检查网络连接")
        print("3. 运行: pip install webdriver-manager")
        print("4. 更新Chrome浏览器到最新版本")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
