"""
配置管理器模块
负责加载和管理所有配置参数
"""

import json
import logging
from typing import Dict, Any


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self._load_config()
        self._setup_logging()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logging(self):
        """设置日志配置"""
        log_config = self.config.get('logging', {})
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s'),
            handlers=[
                logging.FileHandler(log_config.get('file', 'ticket_bot.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def get(self, key: str, default=None):
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_urls(self) -> Dict[str, str]:
        """获取URL配置"""
        return self.config.get('urls', {})
    
    def get_target_selection(self) -> Dict[str, Any]:
        """获取目标选择配置"""
        return self.config.get('target_selection', {})
    
    def get_browser_settings(self) -> Dict[str, Any]:
        """获取浏览器设置"""
        return self.config.get('browser_settings', {})
    
    def get_retry_settings(self) -> Dict[str, Any]:
        """获取重试设置"""
        return self.config.get('retry_settings', {})
    
    def get_selectors(self) -> Dict[str, str]:
        """获取选择器配置"""
        return self.config.get('selectors', {})
    
    def update_config(self, key: str, value: Any):
        """
        更新配置值
        
        Args:
            key: 配置键
            value: 新值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
